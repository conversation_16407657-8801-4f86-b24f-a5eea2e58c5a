'use strict'
const path = require('path')
const pageMethod = require('./util/getPages.js')
const pages = pageMethod.pages()
// const webpack = require('webpack')
const CompressionPlugin = require('compression-webpack-plugin')
// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin
// const productionGzipExtensions = ['js', 'css']
const port = process.env.port || process.env.npm_config_port || 9527 // dev port
// All configuration item explanations can be find in https://cli.vuejs.org/config/
function resolve(dir) {
  return path.join(__dirname, dir)
}
console.log(process.env.NODE_ENV)
const Timestamp = new Date().getTime()
module.exports = {
  transpileDependencies: ['*'],
  pages,
  publicPath: process.env.NODE_ENV === 'development' ? '/bjyshj' : './', // 打包给正式系统
  // publicPath: process.env.VUE_APP_BASE_URL === 'production' ? './' : './', // 打包给本地nginx
  lintOnSave: process.env.NODE_ENV === 'development',
  productionSourceMap: false,
  outputDir: 'dist', // 打包后文件的目录 （默认为dist）
  // assetsDir: 'static',
  devServer: {
    port: port,
    open: true,
    hot: true,
    index: '/login.html',
    overlay: {
      warnings: false,
      errors: true
    },
    proxy: {
      '/bjyshj': {
        // target: 'http://*************:8888/bjyshj/', // 施
        // target: 'http://192.168.1.116:8888/bjyshj/', // 亚茹
        // target: 'http://192.168.1.114:8888/bjyshj/', // 月月
        // target: 'http://192.168.1.100:8888/bjyshj/', // 安晨
        target: 'http://192.168.1.132:8888/bjyshj/', // 高远
        // target: 'http://192.168.1.122:8888/bjyshj/', // 美珍
        // target: 'http://192.168.1.145:8888/bjyshj/', // 喜哥
        // target: 'http://192.168.1.102:8888/bjyshj', // 亚妮
        // target: 'http://192.168.1.212:8888/bjyshj/', // 张绍辉
        // target: 'http://101.132.17.49:8088/bjyshj/', // 线上测试环境
        // target: 'http://192.168.1.145:8888/bjyshj/', // 喜哥
        // target: 'http://192.168.1.207:8888/bjyshj/', // 李江华
        // target: 'http://192.168.1.185:8888/bjyshj/', // wzf
        // target: 'http://192.168.1.157:8888/bjyshj/', // 喜哥
        // target: 'http://192.168.1.110:8888/bjyshj/', // 韩利剑
        // target: 'http://192.168.1.141:8888/bjyshj/', // 王海笑
        // target: 'http://192.168.1.10:8080/bjyshj/',
        changeOrigin: true,
        pathRewrite: {
          '^/bjyshj': ''
        }
      }
    }
  },
  chainWebpack: config => {
    // 网页可以引入svg
    // svg rule loader
    const svgRule = config.module.rule('svg') // 找到svg-loader
    svgRule.uses.clear() // 清除已有的loader, 如果不这样做会添加在此loader之后
    svgRule.exclude.add(/node_modules/) // 正则匹配排除node_modules目录
    // svgRule // 添加svg新的loader处理
    config.module.rules.delete('svg') // 重点:删除默认配置中处理svg,
    config.module
      .rule('svg-sprite-loader')
      .test(/\.svg$/)
      .include
      .add(resolve('src/base/icons')) // 处理svg目录（根据你建的文件路径）
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })

    // 修改images loader 添加svg处理
    const imagesRule = config.module.rule('images')
    imagesRule.exclude.add(resolve('src/base/icons'))

    // 压缩图片
    config.module
      .rule('images')
      .test(/\.(png|jpe?g|gif|svg)(\?.*)?$/)
      .use('image-webpack-loader')
      .loader('image-webpack-loader')
      .options({ bypassOnDebug: true })
      .end()

    // webpack 会默认给commonChunk打进chunk-vendors，所以需要对webpack的配置进行delete
    config.optimization.delete('splitChunks')
  },
  configureWebpack: () => {
    if (process.env.NODE_ENV === 'production' || process.env.NODE_ENV === 'test') {
      return {
        output: { // 输出重构  打包编译后的 文件名称  【模块名称.版本号.时间戳】
          filename: `js/[name].[contenthash].${Timestamp}.js`,
          chunkFilename: `js/[name].[contenthash].${Timestamp}.js`
        },
        resolve: {
          alias: {
            '@project': path.resolve(__dirname, 'src/project/'),
            '@screen': path.resolve(__dirname, 'src/screen/'),
            '@policy': path.resolve(__dirname, 'src/policy/')
          }
        },
        plugins: [
          new CompressionPlugin({
            algorithm: 'gzip',
            test: /\.(js|css)$/, // 匹配文件名
            threshold: 10240, // 对超过10k的数据压缩
            deleteOriginalAssets: false, // 不删除源文件
            minRatio: 0.8 // 压缩比
          }),
          new CompressionPlugin({
            algorithm: 'gzip',
            test: /\.(png|jpe?g|gif|svg)(\?.*)?$/, // 匹配文件名
            threshold: 10240, // 对超过10k的数据压缩
            deleteOriginalAssets: false, // 不删除源文件
            minRatio: 0.8 // 压缩比
          })
          // ,
          // new BundleAnalyzerPlugin(
          //   {
          //     analyzerMode: 'server',
          //     analyzerHost: '127.0.0.1',
          //     analyzerPort: 8888, // 运行后的端口号
          //     reportFilename: 'report.html',
          //     defaultSizes: 'parsed',
          //     openAnalyzer: true,
          //     generateStatsFile: false,
          //     statsFilename: 'stats.json',
          //     statsOptions: null,
          //     logLevel: 'info'
          //   }
          // )
        ]
      }
    } else {
      return {
        resolve: {
          alias: {
            '@project': path.resolve(__dirname, 'src/project/'),
            '@screen': path.resolve(__dirname, 'src/screen/'),
            '@policy': path.resolve(__dirname, 'src/policy/')
          }
        }
      }
    }
  }
}
