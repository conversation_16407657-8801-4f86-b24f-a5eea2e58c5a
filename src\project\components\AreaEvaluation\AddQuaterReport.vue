<template>
  <form-register>
    <validation-observer ref="uqForm">
      <uq-form label-width="140px">
        <uq-date v-model="formData['year']" type="year" label="年份" format="yyyy" value-format="yyyy" width="100%" rules="required" />
        <uq-select v-model="formData['quarter']" label="季度" :select-arr="monthList" rules="required" slim="formItem" />
        <uq-select v-model="formData['templateName']" label="Top/Last确定依据" :select-arr="query?.['reportList']" rules="required" slim="formItem" />
        <!-- <uq-select v-model="formData['areaList']" label="地区" :select-arr="query?.['areaList']" :multiple="true" rules="required" slim="formItem" />-->

      </uq-form>
    </validation-observer>
    <template slot="handleButton">
      <el-button size="small" type="primary" @click="confirm">确定</el-button>
      <el-button type="primary" size="small" @click="$emit('close')">关闭</el-button>
    </template>
  </form-register>
</template>

<script>
import { initEvaluateReportQuarter, createEvaluationReportQuarter } from '../../api/areaEvaluation'
export default {
  name: 'AddQuaterReport',
  data() {
    return {
      formData: {
        areaList: []
      },
      query: {},
      monthList: [
        { dmName: '第一季度', dmValue: '1' },
        { dmName: '第二季度', dmValue: '2' },
        { dmName: '第三季度', dmValue: '3' },
        { dmName: '第四季度', dmValue: '4' }
      ]
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      initEvaluateReportQuarter().then((res) => {
        this.query = res
      })
    },
    confirm() {
      this.$refs.uqForm.validate().then((v) => {
        if (v) {
          this.$uqConfirm('确认生成报告吗？', () => {
            delete this.formData.reportListId
            createEvaluationReportQuarter(this.formData).then(() => {
              this.$message.success('生成成功！')
              this.$emit('save')
            })
          })
        } else {
          this.$message.warning('表单填写有误，请检查后重试。')
        }
      })
    }
  }
}
</script>

<style></style>
