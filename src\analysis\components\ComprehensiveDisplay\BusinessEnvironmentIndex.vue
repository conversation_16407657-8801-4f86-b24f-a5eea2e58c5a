<template>
  <!-- 综合展示-营商环境指数 -->
  <div class="container">
    <div v-show="showPopover" class="mask">
      <div class="popover-item">
        <img class="close" src="../../assets/images/close-dialog.png" alt="" @click="showPopover = false">
        <p style="font-size:20px; font-weight:600;">近15个月营商环境指数趋势</p>
        <div ref="LinChart" class="line-chart" />
      </div>
    </div>

    <div class="circle-box">
      <div class="inner">
        <div>
          <div class="rate" @click="showPopover = true">
            <span slot="reference" class="exponent">{{ businessInfo.exponent || 0 }}</span>
            <img v-if="businessInfo['lifting'] === '-1'" src="../../assets/images/down1.png" alt="">
            <img v-else-if="businessInfo['lifting'] === '1'" src="../../assets/images/up.png" alt="">
            <span v-else class="chiping">持平</span>
            <span class="ring-rate">{{ businessInfo.ringRate ? businessInfo.ringRate + '%' : '' }}</span>
          </div>
          <span>营商环境指数</span><br>
          <span>{{ initYear }}年{{ initMonth }}月</span>
        </div>
      </div>
      <div
        v-for="(item, index) in quotaList"
        :key="index"
        :class="['module-' + index, currentModuleIndex === index ? 'active' : '']"
        class="module"
        @click="moduleClick(item, index)"
      >
        <div class="module-cont">
          <img :src="currentModuleIndex === index ? item.iconActive : item.icon" alt="">
          <p v-html="item.name" />
          <div class="rate">
            <span class="exponent">{{ item.score === '0' || !item.score ? '100.0' : item.score }}</span>
            <img v-if="item['trendFlag'] === '-1'" src="../../assets/images/businessEnvironmentIndex/down.png" alt="">
            <img v-else-if="item['trendFlag'] === '1'" src="../../assets/images/businessEnvironmentIndex/up.png" alt="">
            <span v-else class="chiping">持平</span>
            <span class="ring-rate">{{ item.rate ? item.rate + '%' : '' }}</span>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="bot-cont">
      <div v-for="(item,index) in botDataList" :key="index" class="ct-item active" @click.stop="quotaClick(item)">
        {{ item.quotaName }}
      </div>
    </div> -->
    <div class="second-quota-box" :class="'point-length-' + botDataList.length">
      <div
        v-for="(item, pointIndex) in botDataList"
        :key="item.id"
        class="pointCom"
        :class="['point-item-' + pointIndex, currentModuleIndex]"
      >
        <img src="../../assets/images/businessEnvironmentIndex/secondQuotaPoint.png" alt="指标">
        <div class="quotaNameWord" @click.stop="quotaClick(item)">{{ item.quotaName }}</div>
      </div>
    </div>
    <uq-screen-dialog title="监测评价" :show.sync="showWugan">
      <QuotaStatistics v-if="showWugan" :tab-index="initMonth === 12 ? 2 : 1" :time="quotaTime" :quota-code="quotaCode" />
    </uq-screen-dialog>
  </div>
</template>

<script>
import quotaList from '../../api/quotaList.json'
import QuotaStatistics from '../SenselessMonitoring/QuotaStatistics.vue'
import { getBusinessInfo, getMonitorCityInfo } from '../../api/ComprehensiveDisplay'
import { mapState } from 'vuex'
import { initDate } from '../../api/home'
export default {
  name: 'BusinessEnvironmentIndex',
  components: {
    QuotaStatistics
  },
  data() {
    return {
      currentModuleIndex: 0,
      currentChildIndex: 0,
      quotaList: [],
      leftDataList: [],
      rightDataList: [],
      quotaCode: null,
      botDataList: [],
      showWugan: false,
      activeTab: 1,
      lineChartData: {},
      initYear: null,
      initMonth: null,
      serveTBar: null,
      businessInfo: {
        exponent: 0
      },
      showPopover: false,
      quotaTime: {},
      secondQuotaList: [1, 1, 2, 3, 4]
    }
  },
  computed: {
    ...mapState('indexPage', ['formatTime'])
  },
  watch: {
    formatTime(val) {
      const getDate = val.split('-')
      this.initYear = getDate[0]
      this.initMonth = getDate[1]
      this.getBusinessInfoFunc()
      getMonitorCityInfo({
        year: this.initYear,
        month: this.initMonth
      }).then(res => {
        this.quotaList.forEach((quotaItem, index) => {
          const item = res.find((e) => e.quotaCode === quotaItem.quotaCode)
          if (item) {
            this.$set(quotaItem, 'quotaCode', item.quotaCode)
            this.$set(quotaItem, 'name', item.name)
            this.$set(quotaItem, 'rate', item.rate)
            this.$set(quotaItem, 'score', item.score === '0' ? '100.0' : item.score)
            this.$set(quotaItem, 'trendFlag', item.trendFlag)
          } else {
            this.$set(quotaItem, 'quotaCode', quotaItem.quotaCode)
            this.$set(quotaItem, 'name', quotaItem.quotaName)
            this.$set(quotaItem, 'rate', '0')
            this.$set(quotaItem, 'score', '100.0')
            this.$set(quotaItem, 'trendFlag', '0')
          }
        })
        this.$forceUpdate()
      })
    }
  },
  async  mounted() {
    initDate({ moduleCode: 'zhzs', targetCode: 'base' }).then(res => {
      this.quotaTime = res
    })
    this.quotaList = quotaList
    if (this.formatTime) {
      const getDate = this.formatTime.split('-')
      this.initYear = getDate[0]
      this.initMonth = getDate[1]
      this.getBusinessInfoFunc()
      const res = await getMonitorCityInfo({ year: this.initYear, month: this.initMonth })
      this.quotaList.forEach((quotaItem, index) => {
        const item = res.find((e) => e.quotaCode === quotaItem.quotaCode)
        if (item) {
          this.$set(quotaItem, 'quotaCode', item.quotaCode)
          this.$set(quotaItem, 'name', item.name)
          this.$set(quotaItem, 'rate', item.rate)
          this.$set(quotaItem, 'score', item.score === '0' ? '100.0' : item.score)
          this.$set(quotaItem, 'trendFlag', item.trendFlag)
        } else {
          this.$set(quotaItem, 'quotaCode', quotaItem.quotaCode)
          this.$set(quotaItem, 'name', quotaItem.quotaName)
          this.$set(quotaItem, 'rate', '0')
          this.$set(quotaItem, 'score', '100.0')
          this.$set(quotaItem, 'trendFlag', '0')
        }
      })
      this.$forceUpdate()
    }

    const secondQuotaList = []
    this.quotaList.forEach((item, index) => {
      if (item.quotaCode === 'A') {
        this.$set(item, 'icon', require('../../assets/images/businessEnvironmentIndex/20250114/shichang.png'))
        this.$set(item, 'iconActive', require('../../assets/images/businessEnvironmentIndex/20250114/shichang-a.png'))
      } else if (item.quotaCode === 'B') {
        this.$set(item, 'icon', require('../../assets/images/businessEnvironmentIndex/20250114/fazhi.png'))
        this.$set(item, 'iconActive', require('../../assets/images/businessEnvironmentIndex/20250114/fazhi-a.png'))
      } else if (item.quotaCode === 'C') {
        this.$set(item, 'icon', require('../../assets/images/businessEnvironmentIndex/20250114/touzimaoyi.png'))
        this.$set(item, 'iconActive', require('../../assets/images/businessEnvironmentIndex/20250114/touzimaoyi-a.png'))
      } else if (item.quotaCode === 'D') {
        this.$set(item, 'icon', require('../../assets/images/businessEnvironmentIndex/20250114/zhengwufuwu.png'))
        this.$set(item, 'iconActive', require('../../assets/images/businessEnvironmentIndex/20250114/zhengwufuwu-a.png'))
      } else if (item.quotaCode === 'E') {
        this.$set(item, 'icon', require('../../assets/images/businessEnvironmentIndex/20250114/renwen.png'))
        this.$set(item, 'iconActive', require('../../assets/images/businessEnvironmentIndex/20250114/renwen-a.png'))
      } else if (item.quotaCode === 'F') {
        this.$set(item, 'name', '京津冀<br/>营商环境')
        this.$set(item, 'icon', require('../../assets/images/businessEnvironmentIndex/20250114/jingjinji.png'))
        this.$set(item, 'iconActive', require('../../assets/images/businessEnvironmentIndex/20250114/jingjinji-a.png'))
      }
      item.children.forEach(childItem => {
        secondQuotaList.push({
          ...childItem,
          parentIndex: index
        })
      })
    })
    const halfLength = Math.floor(secondQuotaList.length / 2)
    this.leftDataList = secondQuotaList.slice(0, halfLength)
    this.rightDataList = secondQuotaList.slice(halfLength)

    this.moduleClick(this.quotaList[0], 0)
  },
  methods: {
    getBusinessInfoFunc() {
      getBusinessInfo({
        year: this.initYear,
        month: this.initMonth
      }).then((res) => {
        this.lineChartData = {
          xAxis: [],
          data: []
        }
        this.businessInfo = res
        res.echartsInfo.forEach((v) => {
          this.lineChartData.xAxis.push(v.xname + '月')
          this.lineChartData.data.push(v.yvalue)
        })
        this.initLineChart()
      })
    },
    initLineChart() {
      const option = {
        tooltip: {
          trigger: 'axis',
          appendBody: true,
          backgroundColor: 'rgba(3, 59, 119, 1)',
          borderColor: 'rgba(33, 242, 196, 1)',
          textStyle: {
            color: '#ffffff'
          }
        },
        grid: {
          top: 40,
          left: 30,
          right: 20,
          bottom: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.lineChartData.xAxis,
          axisPointer: {
            type: 'shadow'
          },
          splitLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLine: {
            symbol: ['none'],
            symbolSize: [10, 17],
            lineStyle: {
              color: 'rgba(238,238,238,0.5)',
              width: 1 //  改变坐标线的颜色
            }
          },
          axisLabel: {
            // 调整x轴的lable
            textStyle: {
              fontSize: 12,
              fontWeight: 100,
              color: '#FFFFFF'
            }
          }
        },
        yAxis: {
          type: 'value',
          // name: '(%)',
          alignTicks: true,
          // min: 90.5,
          // max: 92,
          nameTextStyle: {
            color: '#ffffff'
          },
          splitLine: {
            lineStyle: {
              color: '#24326e',
              type: 'dashed'
            }
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#24326e'
            }
          },
          axisTick: {
            show: false
          },
          // 坐标值标注
          axisLabel: {
            show: true,
            textStyle: {
              color: '#BED2F5',
              fontSize: 12
            }
          }
        },
        series: [
          {
            data: this.lineChartData.data,
            type: 'line',
            symbol: 'circle',
            smooth: true,
            itemStyle: {
              normal: {
                color: '#F4AF1B',
                label: {
                  show: true,
                  fontSize: 12,
                  color: '#C5AC54'
                }
              }
            },
            tooltip: {
              valueFormatter: (value) => value
            }
          },
          {
            data: this.lineChartData.data,
            type: 'bar',
            barWidth: '20',
            itemStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: '#3381ff'
                    },
                    {
                      offset: 1,
                      color: '#66a1ff'
                    }
                  ]
                ),
                barBorderRadius: [2, 2, 0, 0]
              }
            },
            tooltip: {
              show: false
            }
          }
        ]
      }
      this.serveTBar = this.$echarts.init(this.$refs['LinChart'])
      let chartOotion = JSON.parse(
        window.sessionStorage.getItem('chartOotion')
      )['option.zhzs.yshjzs']
      chartOotion = JSON.parse(chartOotion)
      if (option.yAxis instanceof Array) {
        option.yAxis[0] = Object.assign({}, option.yAxis[0], chartOotion.yAxis)
      } else {
        option.yAxis = Object.assign({}, option.yAxis, chartOotion.yAxis)
      }
      this.serveTBar.setOption(option)
      const that = this
      window.addEventListener('resize', function () {
        that.serveTBar.resize()
      })
    },
    moduleClick(moduleItem, index) {
      this.quotaCode = moduleItem.quotaCode
      this.botDataList = moduleItem.children
      this.botDataList.forEach((item) => {
        this.$set(item, 'parentIndex', index)
      })
      this.currentModuleIndex = index
      this.currentChildIndex = 0
    },
    quotaClick(item) {
      console.log(item)

      this.activeTab = item.parentIndex + 1
      this.showWugan = true
    }

  }
}
</script>
<style lang="scss"></style>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100vh;
  // background: url(../../assets/images/businessEnvironmentIndex/20250114/bg.png) no-repeat center;
  // background-size: 100% 100%;
  position: relative;

  .mask {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;

    .popover-item {
      z-index: 99;
      position: absolute;
      // width: 29.9479vw;
      width: 1000px;
      height: 24.6875vw;
      box-sizing: border-box;
      padding: 20px 5px 20px 5px;
      background: url("../../assets/images/numBg-2.png") no-repeat center center;
      background-size: 100% 100%;
      top: 50%;
      left: 50%;
      transform: translate(-50%, calc(-50% - 6.25vw));
      background-color: rgba(0, 0, 0, 0);

      >p {
        color: #fff;
        padding-left: 15px;
        margin-top: 5px;
        margin-bottom: 5px;
        text-align: center;
      }

      .line-chart {
        width: 990px;
        height: 22vw;
      }

      .close {
        cursor: pointer;
        position: absolute;
        top: 20px;
        right: 10px;
        width: 20px;
      }
    }
  }

  .circle-box {
    // width: 48.2292vw;
    // height: 34.6979vw;
    width: 48vw;
    height: 36.474vw;
    background: url(../../assets/images/businessEnvironmentIndex/20250114/outer.png) no-repeat center;
    background-size: 100% 100%;
    position: absolute;
    // top: 68%;
    top: 36%;
    left: 50%;
    transform: translate(-50%, calc(-50% - 1.5208vw)) scale(1);
    z-index: 99;

    .inner {
      // width: 28.1667vw;
      // height: 24.3646vw;
      width: 30.4vw;
      height: 27vw;
      position: absolute;
      bottom: 1.1354vw;
      left: 50%;
      transform: translateX(-50%);
      background: url(../../assets/images/businessEnvironmentIndex/20250114/inner.png) no-repeat center;
      background-size: 100% 100%;

      >div {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;

        div.rate {
          .exponent {
            cursor: pointer;
            text-align: center;
            font-weight: 600;
            font-size: 2.3958vw;
            text-shadow: 0 0 0.5208vw rgba(78, 167, 224, 0.5),
              /* 第一层发光 */
              0 0 1.0417vw rgba(78, 167, 224, 0.8),
              /* 第二层发光 */
              0 0 1.5625vw rgba(78, 167, 224, 1),
              /* 第三层发光 */
              0 0 2.0833vw rgba(78, 167, 224, 0.8),
              /* 第四层发光 */
              0 0 2.6042vw rgba(78, 167, 224, 0.5);
            /* 第五层发光 */
          }

          .ring-rate {
            font-size: 0.8333vw;
          }

          >img {
            margin: 0vw 0.2604vw;
            width: 0.7813vw;
          }
        }

        span {
          font-weight: 600;
          font-size: 1.1458vw;
        }
      }
    }

    .module {
      position: absolute;
      background-size: 100% 100%;
      cursor: pointer;

      .module-cont {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5208vw;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        div.rate {
          display: flex;
          align-items: center;
          margin-top: -.5vw;
          text-shadow: 0 0 0.5208vw rgba(78, 167, 224, 0.5),
            /* 第一层发光 */
            0 0 1.0417vw rgba(78, 167, 224, 0.8),
            /* 第二层发光 */
            0 0 1.5625vw rgba(78, 167, 224, 1),
            /* 第三层发光 */
            0 0 2.0833vw rgba(78, 167, 224, 0.8),
            /* 第四层发光 */
            0 0 2.6042vw rgba(78, 167, 224, 0.5);

          /* 第五层发光 */
          .exponent {
            cursor: pointer;
            text-align: center;
            font-weight: 600;
            font-size: 1vw;
          }

          .chiping {
            font-size: .7292vw;
            white-space: nowrap;
            margin: 0 3px;
          }

          .ring-rate {
            font-size: .7292vw;
          }

          >img {
            margin: 0vw 0.2604vw;
            width: 0.5vw;
          }
        }

        p {
          font-weight: 600;
          margin-top: -1.0417vw;
          font-size: 1vw;
          color: #fff;
          text-align: center;
          white-space: nowrap;
          text-shadow: 0 0 0.5208vw rgba(4, 157, 252, 0.5),
            /* 第一层发光 */
            0 0 1.0417vw rgba(4, 157, 252, 0.1),
            /* 第二层发光 */
            0 0 1.5625vw rgba(4, 157, 252, 1),
            /* 第三层发光 */
            0 0 2.0833vw rgba(4, 157, 252, 0.8),
            /* 第四层发光 */
            0 0 2.6042vw rgba(4, 157, 252, 0.5);
          /* 第五层发光 */
        }
      }

      &.active {
        .module-cont {

          p,
          .rate {
            text-shadow: 0 0 0.5208vw rgba(217, 134, 42, 0.5),
              /* 第一层发光 */
              0 0 1.0417vw rgba(217, 134, 42, 0.8),
              /* 第二层发光 */
              0 0 1.5625vw rgba(217, 134, 42, 1),
              /* 第三层发光 */
              0 0 2.0833vw rgba(217, 134, 42, 0.8),
              /* 第四层发光 */
              0 0 2.6042vw rgba(217, 134, 42, 0.5);
            /* 第五层发光 */
          }
        }
      }

      .module-child {
        height: 100%;
        position: absolute;
        transform: translateX(-100%);

        &.module-child-active {
          p {
            color: #ff9915;
            border-bottom: 0.0521vw solid #feac57 !important;
          }
        }

        .module-child-item {
          display: flex;
          align-items: center;
          color: #459cff;
          margin-right: 0;
          position: absolute;
          cursor: pointer;
          height: 4.1667vw;
          z-index: 99;

          p {
            border-bottom: 0.0521vw solid #298af1;
            margin-right: -0.5208vw;
            padding: 0 0.5208vw 0.2604vw 0.2604vw;
            white-space: nowrap;
            font-size: 1.0417vw;
          }
        }

        &.module-child-right {
          left: 0;
          transform: translateX(0%);
          align-items: start;

          p {
            margin-left: -0.5208vw;
            padding-left: 0.625vw;
          }
        }
      }

      &.module-0 {
        // width: 8.3333vw;
        // height: 9.6875vw;
        // bottom: 1.3vw;
        // left: 1.6vw;
        width: 10vw;
        height: 11.711vw;
        bottom: 2.3vw;
        left: 2.3vw;

        &.active {
          background: url(../../assets/images/businessEnvironmentIndex/20250114/module-0.png) no-repeat center;
          background-size: 100% 100%;
        }

        .module-cont {
          padding-left: 0.5208vw;
        }
      }

      &.module-1 {
        // width: 8.6979vw;
        // height: 11.5625vw;
        // bottom: 10.4vw;
        // left: 1.5vw;
        width: 10.6979vw;
        height: 14.348vw;
        bottom: 13.6vw;
        left: 1.9vw;
        transform: rotate(-1deg);

        &.active {
          background: url(../../assets/images/businessEnvironmentIndex/20250114/module-1.png) no-repeat center;
          background-size: 100% 100%;
        }

        .module-cont {
          padding-right: 1.3021vw;
        }
      }

      &.module-2 {
        // width: 12.7604vw;
        // height: 8.2813vw;
        // bottom: 19vw;
        // left: 6.5vw;
        width: 15.6vw;
        height: 10.437vw;
        bottom: 24.4vw;
        left: 8.5vw;

        &.active {
          background: url(../../assets/images/businessEnvironmentIndex/20250114/module-2.png) no-repeat center;
          background-size: 100% 100%;
        }

        .module-cont {
          margin-top: -0.8333vw;
        }
      }

      &.module-3 {
        // width: 12.7604vw;
        // height: 8.2813vw;
        // bottom: 19vw;
        // right: 6.5vw;
        width: 15.604vw;
        height: 10.4026vw;
        bottom: 24.4vw;
        right: 8.5vw;

        &.active {
          background: url(../../assets/images/businessEnvironmentIndex/20250114/module-3.png) no-repeat center;
          background-size: 100% 100%;
        }

        .module-cont {
          margin-top: -0.9896vw;
        }
      }

      &.module-4 {
        // width: 8.6979vw;
        // height: 11.5625vw;
        // bottom: 10.4vw;
        // right: 1.4vw;
        width: 10.69vw;
        height: 14.363vw;
        bottom: 13.6vw;
        right: 1.9vw;

        &.active {
          background: url(../../assets/images/businessEnvironmentIndex/20250114/module-4.png) no-repeat center;
          background-size: 100% 100%;
        }

        .module-cont {
          padding-left: 1.3021vw;
        }
      }

      &.module-5 {
        // width: 8.3333vw;
        // height: 9.6875vw;
        // bottom: 1.3vw;
        // right: 1.6vw;
        width: 10vw;
        height: 11.75vw;
        bottom: 2.5vw;
        right: 2.2vw;

        &.active {
          background: url(../../assets/images/businessEnvironmentIndex/20250114/module-5.png) no-repeat center;
          background-size: 100% 100%;
        }
      }
    }
  }

  .bot-cont {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: .5vw;
    display: flex;
    gap: 0.5208vw;
    z-index: 100;

    .ct-item {
      cursor: pointer;
      font-weight: 600;
      width: 8.6979vw;
      height: 2.1875vw;
      height: 4vh;
      max-height: 2.1875vw;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.7292vw;

      &.active
      {
      background: url(../../assets/images/businessEnvironmentIndex/20250114/right-box-active.png) no-repeat center;
      background-size: 100% 100%;
      text-shadow: 0 0 0.5208vw rgba(217, 134, 42, 0.5),
        /* 第一层发光 */
        0 0 1.0417vw rgba(217, 134, 42, 0.8),
        /* 第二层发光 */
        0 0 1.5625vw rgba(217, 134, 42, 1),
        /* 第三层发光 */
        0 0 2.0833vw rgba(217, 134, 42, 0.8),
        /* 第四层发光 */
        0 0 2.6042vw rgba(217, 134, 42, 0.5);
      /* 第五层发光 */
    }
  }
}

.second-quota-box {
  background: url('../../assets/images/businessEnvironmentIndex/secondQuotaBg.png') no-repeat;
  background-size: 100% 100%;
  width: 100%;
  height: 6.5vw;
  position: absolute;
  // top: 130%;
  top: 64%;
  text-align: center;
  display: flex;
  justify-content: flex-start;

  .pointCom {
    position: absolute;
    width: 25%;
  }

  &.point-length-2 {
    .point-item-0 {
      left: 10vw;
      bottom: -1vh;
    }

    .point-item-1 {
      right: 10vw;
      bottom: -1vh;
    }
  }

  &.point-length-3 {
    .point-item-0 {
      left: 6vw;
      bottom: 0;
    }

    .point-item-1 {
      left: 50%;
      bottom: -0.3vw;
      transform: translateX(-50%);
    }

    .point-item-2 {
      right: 6vw;
      bottom: 0vw;
    }
  }

  &.point-length-4 {
    .point-item-0 {
      left: 3vw;
      bottom: 1vh;
    }

    .point-item-1 {
      left: 13vw;
      bottom: -0.6vh;
    }

    .point-item-2 {
      right: 13vw;
      bottom: -0.6vh;
    }

    .point-item-3 {
      right: 3vw;
      bottom: 1vh;
    }
  }

  &.point-length-5 {
    .point-item-0 {
      left: -0.7vw;
      bottom: 1.5vw
    }

    .point-item-1 {
      left: 8.3vw;
      bottom: 0;
    }

    .point-item-2 {
      left: 50%;
      bottom: -0.3vw;
      transform: translateX(-50%);
    }

    .point-item-3 {
      right: 9vw;
      bottom: 0vw;
    }

    .point-item-4 {
      right: 0vw;
      bottom: 1.5vw;
    }
  }

  img {
    width: 38px;
    height: 36px;
  }

  .quotaNameWord {
    font-family: PingFang SC;
    font-weight: 800;
    font-size: 20px;
    color: #FFFFFF;
    text-shadow: -1px -1px 5px #3DB8FF, 1px 1px 5px #3DB8FF;
    cursor: pointer;
    background: url(../../assets/images/quotaNameWordBg.png) no-repeat center;
    padding: 3px 0;
    background-size: 100% 100%;
  }
}

@media (min-width: 135.4167vw) {
  .circle-box {
    transform: translate(-50%, calc(-50% - 0.5208vw)) scale(1.2);
  }
}

@media (max-height: 1000px) {
  .circle-box {
    transform: translate(-50%, calc(-50% - 0.5vw)) scale(.93);
  }
}
}
</style>
