import request from '@/base/request'
import store from '../../analysisManage/store'

const NS = store.state['contextPath']

// 评估模版配置-月度-评估模版配置-列表
export const monthListReportInfo = `${NS}/report/monthListReportInfo`
// 评估模版配置-月度-评估模版配置-总数
export const monthListReportInfoCount = `${NS}/report/monthListReportInfoCount`

// 评估模版配置-季度-评估模版配置-列表
export const quarterListReportInfo = `${NS}/report/quarterListReportInfo`
// 评估模版配置-季度-评估模版配置-总数
export const quarterListReportInfoCount = `${NS}/report/quarterListReportInfoCount`

// 评估模版配置-通用-保存表单
export function saveForm(data) {
  return request({
    url: `${NS}/report/saveForm`,
    method: 'post',
    headers: {
      smSwitch: 1
    },
    data
  })
}
// 评估模版配置-通用-删除
export function deleteById(data) {
  return request({
    url: `${NS}/report/deleteById`,
    method: 'post',
    headers: {
      smSwitch: 1
    },
    data
  })
}

// 评估模版配置-通用-初始化
export function init(data) {
  return request({
    url: `${NS}/report/init`,
    method: 'post',
    data
  })
}

// 评估模版配置-月度-打开表单
export function monthOpenForm(data) {
  return request({
    url: `${NS}/report/monthOpenForm`,
    method: 'post',
    data
  })
}

// 评估模版配置-季度-打开表单
export function quarterOpenForm(data) {
  return request({
    url: `${NS}/report/quarterOpenForm`,
    method: 'post',
    data
  })
}

// 月度报告-列表
export const pageEvaluateReportMonth = `${NS}/reportMonth/pageEvaluateReportMonth`
// 月度报告-总数
export const countPageEvaluateReportMonth = `${NS}/reportMonth/countPageEvaluateReportMonth`
// 月度报告-列表参数初始化
export function reportMonthInitParam(data) {
  return request({
    url: `${NS}/reportMonth/initParam`,
    method: 'post',
    data
  })
}
// 月度报告-创建评估报告
export function createEvaluationReportMonth(data) {
  return request({
    url: `${NS}/reportMonth/createEvaluationReportMonth`,
    method: 'post',
    data
  })
}
// 季度报告-列表
export const pageEvaluateReportQuarter = `${NS}/reportQuarter/pageEvaluateReportQuarter`
// 季度报告-总数
export const countPageEvaluateReportQuarter = `${NS}/reportQuarter/countPageEvaluateReportQuarter`
// 季度报告-列表参数初始化
export function reportQuarterInitParam(data) {
  return request({
    url: `${NS}/reportQuarter/initParam`,
    method: 'post',
    data
  })
}
// 季度报告-列表参数初始化
export function initEvaluateReportQuarter(data) {
  return request({
    url: `${NS}/reportQuarter/initEvaluateReportQuarter`,
    method: 'post',
    data
  })
}
// 季度报告-创建评估报告
export function createEvaluationReportQuarter(data) {
  return request({
    url: `${NS}/reportQuarter/createEvaluationReportQuarter`,
    method: 'post',
    data
  })
}

// 季度报告-计算同比
export function calcYoyAndQoq(data) {
  return request({
    url: `${NS}/olap/wgjcData/calcYoyAndQoq`,
    method: 'post',
    data
  })
}
