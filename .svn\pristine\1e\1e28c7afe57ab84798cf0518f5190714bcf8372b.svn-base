import request from '@/base/request'
import store from '../store'

const NS = store.state['contextPath']
// 角色菜单 以及 用户登录信息
export function getUserAuth(data) {
  return request({
    url: `${NS}/home/<USER>
    type: 'query',
    method: 'post',
    data
  })
}
// 判断是否拥有服务包系统角色
export function checkFwbRole(data) {
  return request({
    url: `${NS}/home/<USER>
    method: 'post',
    data
  })
}
// 系统退出
export function logout(data) {
  return request({
    url: `${NS}/logout`,
    type: 'query',
    method: 'post',
    data
  })
}

// 未读、全部列表
export function listPageMessage(data) {
  return request({
    url: `${NS}/common/message/listPageMessage`,
    method: 'post',
    data
  })
}
// 飘窗获取通知通告
export function getNoticeInfo(data) {
  return request({
    url: `${NS}/notice/getNoticeInfo`,
    type: 'query',
    method: 'post',
    custom: 'part',
    data
  })
}

// 未读、全部列表总数，首页消息提醒总数
export function countPageMessage(data) {
  return request({
    url: `${NS}/common/message/countPageMessage`,
    method: 'post',
    data
  })
}

// 标记已读
export function setRead(data) {
  return request({
    url: `${NS}/common/message/read`,
    method: 'post',
    data
  })
}

// 标记全部已读
export function setReadAll(data) {
  return request({
    url: `${NS}/common/message/readAll`,
    method: 'post',
    data
  })
}

export function getOption(data) {
  return request({
    url: `${NS}/olap/getOption`,
    method: 'post',
    data
  })
}

// 修改密码
export function updatePassword(data) {
  return request({
    url: `${NS}/common/updatePassword`,
    method: 'post',
    data
  })
}
