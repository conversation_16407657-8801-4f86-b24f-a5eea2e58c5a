<!-- 季报告=>QuarterReport -->
<template>
  <grid-page ref="gridList" :columns="tableColumn" :url="listUrl" :count-url="listCountUrl" method="post" :def-height="55" :query-term.sync="searchData" :search-item="searchItem" @search="search">
    <template slot="handle" slot-scope="{ row }">
      <grid-btn-list v-if="row['buttonList']" :btn-list="row['buttonList']" @handleFun="(btn) => handleFun(row, btn)" />
    </template>
    <template slot="search">
      <el-button size="small" type="primary" @click="reset">重置</el-button>
      <el-button size="small" type="primary" @click="createReport">生成报告</el-button>
      <el-button size="small" type="primary" @click="calcYoyAndQoq">计算同比</el-button>
    </template>
    <uq-dialog :show.sync="showDialog" :title="dialogName" height="" width="700px">
      <AddQuaterReport v-if="showDialog" @close="close" @save="save" />
    </uq-dialog>
    <uq-dialog :show.sync="showPreview" :title="previewName" :fullscreen="true">
      <preview-file v-if="showPreview" :id="previewFileId" />
    </uq-dialog>
  </grid-page>
</template>

<script>
import { installWpsAddin, WpsAddonMgr } from '../../../components/common/editWord.js'
import { onlineEditDoc } from '../../../components/common/openwps.js'
import AddQuaterReport from '../../../components/AreaEvaluation/AddQuaterReport.vue'
import { pageEvaluateReportQuarter, countPageEvaluateReportQuarter, reportQuarterInitParam, calcYoyAndQoq } from '../../../api/areaEvaluation'
export default {
  name: 'QuarterReport',
  components: {
    AddQuaterReport
  },
  data() {
    return {
      listUrl: pageEvaluateReportQuarter,
      listCountUrl: countPageEvaluateReportQuarter,
      showDialog: false,
      dialogName: '',
      paramData: {},
      tableColumn: [
        { prop: 'num', label: '序号', minWidth: 60, align: 'center' },
        { label: '报告名称', prop: 'name' },
        { label: 'Top/Last确定依据', align: 'center', prop: 'templateName' },
        { label: '季度', align: 'center', prop: 'periodName' },
        { label: '地区', align: 'center', prop: 'areaName' },
        { label: '报告生成时间', align: 'center', prop: 'reportTime' },
        { label: '操作', minWidth: '200', slotColumnName: 'handle', fixed: 'right' }
      ],
      searchData: {},
      query: {},
      monthList: [
        { dmName: '第一季度', dmValue: '1' },
        { dmName: '第二季度', dmValue: '2' },
        { dmName: '第三季度', dmValue: '3' },
        { dmName: '第四季度', dmValue: '4' }
      ],
      previewFileId: '',
      previewName: '',
      showPreview: false
    }
  },
  computed: {
    searchItem() {
      return [
        [
          { type: 'date', name: 'year', dateType: 'year', format: 'yyyy', valueFormat: 'yyyy', label: '年度', span: 5 },
          { type: 'select', name: 'quarter', label: '季度', arr: this.monthList, span: 5 },
          { type: 'select', name: 'areaCode', label: '地区', arr: this.query['areaList'], span: 5 },
          { type: 'search', label: '查询', span: 4 }
        ]
      ]
    }
  },
  mounted() {
    this.$nextTick(() => {
      // 初始化调用方法
      installWpsAddin(() => {
        if (WpsAddonMgr && WpsAddonMgr.ClientType && WpsAddonMgr.ClientType?.wps) {
          WpsAddonMgr.RegWebNotify(WpsAddonMgr?.ClientType?.wps, 'WpsOAAssist', message => {
            console.log(message)
          })
        }
      })
    })
    reportQuarterInitParam().then((res) => {
      this.query = res
    })
  },
  methods: {
    search() {
      this.$refs.gridList.listSearchFun(this.searchData)
    },
    reset() {
      this.searchData = {}
      this.search()
    },
    close(data) {
      this.showDialog = false
      if (data) this.search()
    },
    createReport() {
      this.dialogName = '生成报告'
      this.showDialog = true
    },
    save() {
      this.showDialog = false
      this.search()
    },
    handleFun(row, btn) {
      if (btn.id === 'VIEW') {
        // this.previewFileId = row.docId
        // this.previewName = row.name
        // this.showPreview = true
        onlineEditDoc({
          docId: row.docId,
          openType: {
            protectType: 3
          }
        })
      } else if (btn.id === 'DOWNLOAD') {
        this.downLoadFile(row.docId)
      }
    },
    // 计算同比
    calcYoyAndQoq() {
      this.$uqConfirm('是否确认计算同比？', () => {
        calcYoyAndQoq().then((res) => {
          this.$message.success('计算成功！' + res)
          this.search()
        })
      })
    }
  }
}
</script>

<style scoped lang="scss"></style>
