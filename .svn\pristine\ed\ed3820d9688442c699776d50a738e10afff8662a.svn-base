<template>
  <form-register>
    <validation-observer ref="pwdInfo">
      <uq-form :model="pwdInfo" label-width="80px">
        <uq-input v-model="pwdInfo['userOldPassword']" :show-password="true" label="原密码" rules="required" />
        <uq-input v-model="pwdInfo['userPassword']" :show-password="true" label="新密码" rules="required|pwd_base|pwd_no_repeat" />
        <uq-input v-model="pwdInfo['userConfirmPassword']" :show-password="true" label="确认密码" rules="required" />
      </uq-form>
    </validation-observer>
    <template slot="handleButton">
      <el-button size="small" type="primary" @click="savePwd">确认</el-button>
      <el-button size="small" type="primary" @click="$emit('close')">关闭</el-button>
    </template>
  </form-register>

</template>

<script>
import { updatePassword } from '../../api/home'
import { encryptSM3 } from '@/base/sm3-1.0.1' //
export default {
  name: 'ChangePassWord',
  data() {
    return {
      pwdInfo: {
        userOldPassword: '',
        userPassword: '',
        userConfirmPassword: ''
      }
    }
  },
  methods: {
    savePwd() {
      if (this.pwdInfo['userPassword'] !== this.pwdInfo['userConfirmPassword']) {
        this.$message.warning('两次密码不一致，请重新输入')
      } else {
        this.$refs.pwdInfo.validate().then(v => {
          if (v) {
            this.$uqConfirm('确定修改密码吗？', () => {
              updatePassword({
                ...this.pwdInfo,
                userOldPassword: encryptSM3(this.pwdInfo['userOldPassword'])
              }).then(() => {
                this.$emit('close')
                this.$message.success('密码修改成功。')
              })
            })
          } else {
            this.$message.warning('密码填写有误')
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
