<template>
  <form-register>
    <uq-form label-width="130px">
      <validation-observer ref="chartForm">
        <el-row>
          <el-col :span="8"><uq-input v-model="formData['name']" rules="required" placeholder="请输入数据项名称" label="数据项名称" /></el-col>
          <el-col :span="8"><uq-input v-model="formData['unit']" rules="required" placeholder="请输入单位(%，个...)" label="数据项单位" /></el-col>
          <el-col :span="8"> <uq-cascader ref="sourceDept" v-model="formData.dataSourceDept" placeholder="请选择数据来源部门" cas-width="100%" slim="formItem" :filterable="true" :props="sourceProp" :options="result['unitList']" label="数据来源部门" /></el-col>
        </el-row>
        <el-row>
          <el-col :span="8"><uq-input v-model="formData['dataSourceSystem']" placeholder="请输入数据来源系统平台" label="数据来源系统平台" /></el-col>
          <el-col :span="8"> <uq-select v-model="formData['collectTypeList']" label="数据采集形式" multiple placeholder="请选择数据采集形式" :select-arr="result['configCollectTypeList']" slim="formItem" /></el-col>
          <el-col :span="8"> <uq-select v-model="formData['collectFrequency']" label="实际更新频率" placeholder="请选择实际更新频率" :select-arr="result['collectFrequencyList']" slim="formItem" /></el-col>
        </el-row>
        <el-row>
          <el-col :span="8"> <uq-select v-model="formData['showPeriod']" label="展示周期CODE" placeholder="请选择展示周期CODE" :select-arr="result['showPeriodList']" slim="formItem" /></el-col>
          <el-col :span="8"> <uq-select v-model="formData['calcType']" label="统计类型" placeholder="请选择统计类型" :select-arr="result['calcTypeList']" slim="formItem" /></el-col>
          <el-col :span="8">
            <uq-radio v-model="formData['nationFlag']" label="是否国发指标" :select-arr="result['sysYnList']" :just-value="true" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8"> <uq-select v-model="formData['districtEvaluateFlag']" label="是否纳入评价" placeholder="请选择是否纳入评价" :select-arr="result['sysYnList']" slim="formItem" /></el-col>
          <el-col :span="8"> <uq-select v-model="formData['districtFlag']" label="是否可分区" placeholder="请选择是否可分区" :select-arr="result['sysYnList']" slim="formItem" /></el-col>
          <el-col :span="8">
            <uq-select v-model="formData['calculateWay']" slim="formItem" label="价值判定" :rules="formData['formulaCategory']==='2'?'':'required'" :select-arr="result['calculateWayList']" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <uq-select v-model="formData['dataProvideMode']" label="数据提供方式" slim="formItem" :select-arr="result['dataProvideModeList']" />
          </el-col>
          <el-col :span="8">
            <uq-select v-model="formData['dataEstimateProvideTime']" label="数据预计回收时间" slim="formItem" :select-arr="result['dataEstimateProvideTimeList']" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <uq-radio v-model="formData['chartType']" label="图形类型" :select-arr="result['chartTypeList']" :just-value="true" />
          </el-col>
          <el-col :span="8">
            <uq-radio v-model="formData['showFlag']" label="是否展示" :select-arr="result['sysYnList']" :just-value="true" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <uq-radio v-model="formData['olapField']" label="统计维度" :select-arr="result['olapFieldList']" :just-value="true" />
          </el-col>
          <el-col :span="8">
            <uq-radio v-model="formData['orderFlag']" label="是否正序" :select-arr="result['sysYnList']" :just-value="true" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <uq-input v-model="formData['rangeLow']" label="最小值" />
          </el-col>
          <el-col :span="8">
            <uq-input v-model="formData['rangeHigh']" label="最大值" />
          </el-col>
          <el-col :span="8">
            <uq-input v-model="formData['rangeScale']" label="范围精度" type="number" rules="float:0,4" />
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="8">
            <uq-input v-model="formData['rangeMin']" label="最小值限制" type="number" rules="float:0,4" />
          </el-col>
          <el-col :span="8">
            <uq-input v-model="formData['rangeMax']" label="最大值限制" type="number" rules="float:0,4" />
          </el-col>

        </el-row>
        <el-row>
          <el-col :span="8">
            <uq-radio v-model="formData['trendChartFlag']" label="显示趋势图" :select-arr="result['sysYnList']" :just-value="true" />
          </el-col>
          <el-col v-if="formData['trendChartFlag'] === '1'" :span="8">
            <uq-radio v-model="formData['trendChartType']" label="趋势图类型" :select-arr="result['chartTypeList']" :just-value="true" />
          </el-col>
        </el-row>
        <uq-radio v-model="formData['lineFlag']" label="显示水平线" :select-arr="result['sysYnList']" :just-value="true" @change="levelClick('change')" />
        <template v-if="formData['lineFlag']==='1'">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <el-button type="primary" size="small" @click="levelClick('save')">添加</el-button>
            </div>
            <div v-for="(item,index) in levelList" :key="'rangeList'+index" class="range-item">
              <div>
                <uq-input v-model="item['lineName']" :error-name="'第'+(index+1)+'个水平线名称'" label="水平线名称" label-width="120px" />
              </div>
              <div>
                <uq-select v-model="item['lineDataType']" slim="formItem" :error-name="'第'+(index+1)+'个数据类型'" label="水平线数据类型" label-width="140px" rules="required" :select-arr="result['lineDataTypeList']" />
              </div>
              <div>
                <uq-input v-model="item['lineDataValue']" :error-name="'第'+(index+1)+'个数据值'" label="水平线数据值" label-width="140px" :rules="item['lineDataType'] === '3' ? 'required|floatNeg:0,4' : 'floatNeg:0,4'" />
              </div>
              <el-button style="margin-left: 15px;" type="primary" size="small" @click="levelClick('delete',index)">删除</el-button>
            </div>
          </el-card>
        </template>
        <uq-input v-model="formData['yaxisValue']" label="自定义<br>Y轴数据" type="textarea" />

        <el-row>
          <el-col :span="8">
            <el-form-item label="预警开关">
              <el-switch v-model="formData['warnOpenFlag']" active-color="#13ce66" inactive-color="#DCDFE6" active-value="1" inactive-value="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="formData['warnOpenFlag']==='1'">
          <el-col :span="8">
            <uq-select v-model="formData['warnType']" label="预警类型" slim="formItem" :select-arr="result['warnTypeList']" />
          </el-col>
          <el-col :span="8">
            <uq-select v-model="formData['warnOperator']" label="预警运算符" slim="formItem" :select-arr="result['quotaWarnOperatorList']" />
          </el-col>
          <el-col :span="8">
            <uq-input v-model="formData['warnValue']" label="预警值" type="number" rules="float:0,4" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <uq-select v-model="formData['formulaCategory']" label="类别" slim="formItem" rules="required" :select-arr="result['formulaCategoryList']" />
          </el-col>
          <el-col :span="8">
            <uq-select v-model="formData['dataScoreConfig']" label="无数据得分" slim="formItem" rules="required" :select-arr="result['dataScoreConfigList']" />
          </el-col>
          <el-col v-if="formData['dataScoreConfig']==='2'" :span="8">
            <uq-input v-model="formData['defaultScore']" label="默认得分" type="number" rules="required|float:0,4|min_value:0|max_value:100" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <uq-select v-model="formData['formulaType']" label="计算方法" slim="formItem" :rules="formData['formulaCategory']==='2'?'':'required'" :select-arr="result['formulaTypeList']" @change="selectFormulaType" />
          </el-col>
          <el-col :span="8">
            <uq-input v-model="formData['fullMarks']" label="满分" type="number" :rules="formData['formulaCategory']==='2'?'float:0,4':'required|float:0,4'" />
          </el-col>
        </el-row>
        <template v-if="formData['formulaType']==='1'">
          <el-row>
            <el-col :span="6">
              <uq-input v-model="formulaConfig['factor']" label="系数" type="number" :rules="formData['formulaCategory']==='2'?'float:0,4':'required|float:0,4'" />
            </el-col>
            <el-col :span="6">
              <uq-input v-model="formulaConfig['defaultValue']" label="默认值" type="number" :rules="formData['formulaCategory']==='2'?'float:0,4':'required|float:0,4'" />
            </el-col>
            <el-col :span="6">
              <uq-input v-model="formulaConfig['minValue']" label="最小值" type="number" rules="float:0,4" />
            </el-col>
            <el-col :span="6">
              <uq-input v-model="formulaConfig['maxValue']" label="最大值" type="number" rules="float:0,4" />
            </el-col>
          </el-row>

        </template>
        <template v-if="formData['formulaType']==='2'||formData['formulaType']==='3'">
          <el-card class="box-card">
            <div v-if="formData['formulaType']==='2'" slot="header" class="clearfix">
              <el-button type="primary" size="small" @click="handleClick('save')">添加</el-button>
            </div>
            <div v-for="(item,index) in formulaConfig.rangeList" :key="'rangeList'+index" class="range-item">
              <div>
                <uq-input v-model="item['range']" :error-name="'第'+(index+1)+'个区间表达式'" label="区间表达式" rules="required" />
              </div>
              <div>
                <uq-input v-model="item['value']" :error-name="'第'+(index+1)+'个得分'" label="得分" type="number" rules="required|float:0,4" />
              </div>
              <el-button v-if="formData['formulaType']==='2'" style="margin-left: 15px;" type="primary" size="small" @click="handleClick('delete',index)">删除</el-button>
            </div>
          </el-card>
        </template>
        <uq-input v-model="formData['remark']" type="textarea" label="备注" />
      </validation-observer>
    </uq-form>
    <template slot="handleButton">
      <el-button type="primary" size="small" @click="saveFun">保存</el-button>
      <el-button type="primary" size="small" @click="$emit('close')">关闭</el-button>
    </template>
  </form-register>
</template>

<script>

export default {
  name: 'ChartForm',
  props: {
    obj: {
      type: Object,
      default: null
    },
    result: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      sourceProp: {
        label: 'label',
        value: 'value',
        emitPath: false
      },
      formulaConfig: {},
      levelList: [{ lineName: '', lineDataType: '', lineDataValue: '' }],
      formData: {}
    }
  },
  created() {
    if (this.obj) {
      this.formData = Object.assign({}, this.obj)
      this.formData.collectTypeList = this.formData.collectType ? this.formData.collectType.split(',') : []
      this.levelList = this.obj?.lineJson ? JSON.parse(this.obj.lineJson) : []
      this.formulaConfig = this.obj.formulaConfig ? JSON.parse(this.obj.formulaConfig) : {}
    }
  },
  methods: {
    selectFormulaType(data) {
      if (data === '2') {
        this.formulaConfig.rangeList = [{ range: '', value: '' }]
      } else if (data === '3') {
        this.formulaConfig.rangeList = [{ range: '', value: '' }, { range: '', value: '' }]
      } else {
        this.formulaConfig = {}
      }
    },
    levelClick(type, index) {
      const that = this
      if (type === 'save') {
        that.levelList.push({ lineName: '', lineDataType: '', lineDataValue: '' })
      } else if (type === 'change') {
        that.levelList = [{ lineName: '', lineDataType: '', lineDataValue: '' }]
      } else {
        if (that.levelList.length > 1) {
          that.levelList.splice(index, 1)
        }
      }
    },
    handleClick(type, index) {
      const that = this
      if (type === 'save') {
        that.formulaConfig.rangeList.push({ range: '', value: '' })
        console.log(that.formulaConfig.rangeList)
      } else {
        if (that.formulaConfig.rangeList.length > 1) {
          that.formulaConfig.rangeList.splice(index, 1)
        }
      }
      this.$forceUpdate()
    },
    saveFun() {
      this.$refs.chartForm.validate().then(v => {
        if (v) {
          const node = this.$refs.sourceDept.getCheckedNodes()[0]
          this.formData.dataSourceDeptName = node.label
          this.formData.collectType = this.formData.collectTypeList.join(',')
          this.formData.lineJson = JSON.stringify(this.levelList)
          this.formData.formulaConfig = JSON.stringify(this.formulaConfig)
          this.formData.formulaConfigData = this.formulaConfig
          this.$emit('save', this.formData)
        } else {
          this.$message.warning('表单填写有误，请检查后重试。')
        }
      })
    }
  }
}
</script>

<style scoped>
</style>
<style lang="scss" scoped>
.box-card {
  margin-bottom: 20px;
  margin-left: 100px;
  :deep(.el-card__header) {
    display: flex;
    padding: 10px 20px;
    align-items: center;
    justify-content: flex-end;
  }
  .range-item {
    display: flex;
    align-items: flex-start;
    justify-content: center;
  }
}
</style>
