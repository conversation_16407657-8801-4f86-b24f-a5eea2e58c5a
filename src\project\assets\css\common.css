@charset "UTF-8";
* {
  margin: 0;
  padding: 0;
  outline: none;
  box-sizing: border-box;
  list-style: none;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB",
 "Microsoft YaHei", "微软雅黑", Aria<PERSON>, sans-serif;
}

@font-face {
  font-family: "YouSheBiaoTiHei";
  src: url("fonts/YouSheBiaoTiHei.ttf?t=148972183227923") format("truetype");
  /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
}

@font-face {
  font-family: "PangmenZhengdao";
  src: url("fonts/PangmenZhengdao.ttf?t=148972183227923") format("truetype");
  /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
}

@font-face {
  font-family: "QTypeSquare-Medium";
  src: url("fonts/QTypeSquare-Medium.otf?t=1660804400614") format("truetype");
  /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
}

@font-face {
  font-family: 'bjysIcon';
  src: url("./fonts/iconfont.woff2?t=1652685445303") format("woff2"), url("./fonts/iconfont.woff?t=1652685445303") format("woff"), url("./fonts/iconfont.ttf?t=1652685445303") format("truetype");
}

.bjysIcon {
  font-family: "bjysIcon" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.el-table td.el-table__cell,
.el-table th.el-table__cell.is-leaf {
  border: none !important;
}

.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
  background-color: #f4f5f8 !important;
}

.el-table th,
.el-table tr:nth-child(2n) td {
  background-color: #f4f5f8 !important;
}

.el-table .el-table__header-wrapper tr th,
.el-table .el-table__fixed-header-wrapper tr th {
  font-size: 16px;
  height: 57px !important;
}

.el-table thead {
  color: #3c3c4c !important;
}

.switch-tabs {
  padding: 0 28px;
  background-color: #fff;
  border-bottom: 1px solid #f3f4f9;
  box-sizing: border-box;
  margin-bottom: 16px;
}

.switch-tabs .el-tabs__nav-wrap::after {
  height: 0px;
}

.switch-tabs .el-tabs__item.is-active {
  color: #333;
  font-weight: 600;
}

.switch-tabs .el-tabs__header {
  margin-bottom: 0px;
}

.switch-tabs .el-tabs__nav .el-tabs__item {
  font-size: 16px;
}

.keyDownFun-focus {
  width: 0;
  height: 0;
  position: absolute;
  overflow: hidden;
  opacity: 0;
}

.ysbtblack {
  font-family: "YouSheBiaoTiHei", serif !important;
  font-size: 14px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  zoom: 1;
}

.c-container {
  height: 100%;
  background-color: #ffffff;
}

.c-container .el-tabs__header {
  margin: 0;
}

.box-el_card {
  height: 99.5%;
}

.box-el_card .el-card__body {
  padding: 0;
  height: calc(100% - 44px);
}

.box-el_card .el-card__header {
  padding: 12px 20px;
  background-color: #ffffff;
}

.box-el_card .el-card__header .box-card-right_search {
  display: block;
  float: right;
  margin-top: -4px;
}

.flr {
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
}

.flc {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.fc {
  justify-content: center;
  align-items: center;
  justify-items: center;
  align-content: center;
}

.font-bold {
  font-weight: bold;
}

.align-center {
  text-align: center;
}

.align-right {
  text-align: right;
}

.align-left {
  text-align: left;
}

.icon-size-max {
  font-size: 50px;
}

.icon-size-medium {
  font-size: 25px;
}

.icon-size-small {
  font-size: 15px;
}

.icon-size-mini {
  font-size: 12px;
}

/* 左右居中 */
.row-center {
  margin: 0 auto;
}

/* 最大宽度 */
.max-width {
  max-width: 1200px;
}

/* 距离顶部距离 */
.m-t-mini {
  margin-top: 5px;
}

.m-t-small {
  margin-top: 10px;
}

.m-t-middle {
  margin-top: 20px;
}

.m-t-medium {
  margin-top: 30px;
}

/* 距离底部距离 */
.m-b-mini {
  margin-bottom: 5px;
}

/* 距离右部距离 */
.m-r-mini {
  margin-right: 5px;
}

.m-r-small {
  margin-right: 10px;
}

.m-r-middle {
  margin-right: 20px;
}

.m-r-medium {
  margin-right: 30px;
}

/* 距离右部距离 padding */
.p-r-mini {
  padding-right: 5px;
}

.p-r-small {
  padding-right: 10px;
}

.p-r-middle {
  padding-right: 20px;
}

.p-r-medium {
  padding-right: 30px;
}

/* 距离左部距离 padding */
.p-l-mini {
  padding-left: 5px;
}

.p-l-small {
  padding-left: 10px;
}

.p-l-middle {
  padding-left: 20px;
}

.p-l-medium {
  padding-left: 30px;
}

.p-t-middle {
  padding-top: 20px;
}

.p-t-mini {
  padding-top: 5px;
}

.p-b-mini {
  padding-bottom: 5px;
}

.p-b-small {
  padding-bottom: 10px;
}

/* 边线颜色 */
.right-line {
  border-right: 1px solid #dee5ef;
}

.bottom-line {
  border-bottom: 1px solid #dee5ef;
}

.right-line-dashed {
  border-right: 1px dashed #dee5ef;
}

/* 颜色 */
.color-blue {
  color: #0c6dd1;
}

.color-pink {
  color: #ff602d;
}

.color-green {
  color: #009d29;
}

.color-yellow {
  color: #fec171;
}

.color-brown {
  color: #d0703c;
}

.color-white {
  color: #ffffff;
}

.color-tip_info {
  color: #fd990b;
}

.color-555 {
  color: #555555 !important;
}

.color-666 {
  color: #666666 !important;
}

.color-999 {
  color: #999999 !important;
}

.color-ccc {
  color: #cccccc !important;
}

.color-red {
  color: red !important;
}

.color_green_light {
  color: #05cf94 !important;
}

.color-orange {
  color: #fd990b !important;
}

.cursor-pointer {
  cursor: pointer;
}

.delete-line_middle {
  text-decoration: line-through;
  text-decoration-color: red;
}

.hover-line:hover {
  text-decoration: underline;
  color: #310400;
}

.el-select-dropdown {
  max-width: 500px;
}

::-webkit-scrollbar-track-piece {
  background: #d3dce6;
}

::-webkit-scrollbar {
  width: 4px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background: #99a9bf;
  border-radius: 20px;
}

/*滚动条样式*/
.over-auto {
  overflow-y: auto !important;
}

.over-auto::-webkit-scrollbar {
  width: 3px;
  height: 4px;
}

.over-auto::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.4);
}

.el-input.is-disabled .el-input__inner,
.el-textarea.is-disabled .el-textarea__inner,
.el-checkbox__input.is-disabled + span.el-checkbox__label {
  color: #012956;
  cursor: default;
}

.form-mini-input .el-input-group__append {
  padding: 0 10px;
}

.el-input--prefix .el-input__inner {
  padding-left: 24px;
}

.el-input--suffix .el-input__inner {
  padding-right: 24px;
}

.el-input__prefix {
  left: 2px;
}

.el-input__suffix {
  right: 0;
}

.slide-fade-enter-active {
  transition: all 0.3s ease;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter,
.slide-fade-leave-to {
  transform: translateX(10px);
  opacity: 0;
}

.task-container {
  padding: 0 10px 10px;
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.task-container .grid-page {
  margin-top: 10px;
  flex: 1;
  height: calc(100% - 191px) !important;
}

.task-container .grid-page tr {
  font-size: 15px;
  color: #3d3d4d;
}

.task-container .el-cascader {
  width: 100%;
}

.task-content {
  display: flex;
  align-items: center;
}

.task-content > div {
  display: flex;
}

.task-content > div img {
  width: 20px;
  height: 20px;
  margin-right: 5px;
}

.task-content > div .duban {
  cursor: pointer;
}

.task-count {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.task-count .task-counts {
  height: 27px;
  display: flex;
  align-items: center;
}

.task-count .task-counts .task-counts-item {
  padding: 0 18px;
  display: flex;
  align-items: center;
  height: 27px;
  border-left: 1px dashed #333333;
}

.task-count .task-counts .task-counts-item:first-child {
  border: none;
}

.task-count .task-counts .task-counts-item div {
  margin: 0 15px;
  cursor: pointer;
  height: 27px;
  line-height: 27px;
  text-align: center;
  width: 65px;
  border-radius: 33px;
}

.task-count .task-counts .task-counts-item div.gray {
  background-color: #f3f5f7;
}

.task-count .task-counts .task-counts-item div.gray:hover {
  background-color: rgba(57, 144, 233, 0.4) !important;
}

.task-count .task-counts .task-counts-item div.count-active {
  font-weight: 600;
  background-color: rgba(32, 67, 147, 0.7) !important;
}

.message-popper {
  padding: 0;
  border: none;
  top: 40px !important;
}

.c-table-main thead {
  color: #333;
}

.c-table-main thead th {
  background-color: #f4f5f8 !important;
}

.c-table-main .cell {
  font-size: 16px;
}

.c-table-main td,
.c-table-main th {
  height: 56px;
  line-height: 56px;
  padding: 0 !important;
}

.grid-page tr {
  font-size: 16px;
}

.text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.custom-form-style .no-label-style {
  border: none !important;
}

.custom-form-style .no-label-style .el-form-item {
  border: none;
}

.custom-form-style .no-label-style .el-form-item__label {
  display: inline-block !important;
  background-color: transparent !important;
}

.custom-form-style .no-label-style .el-input__inner,
.custom-form-style .no-label-style .el-textarea__inner {
  border-radius: 5px !important;
  border: 1px solid #dcdfe6 !important;
}

.custom-form-style .no-label-style .el-form-item__content {
  padding-left: 0px !important;
  border: none !important;
}

.custom-form-style .el-form-item {
  border: 1px solid #ced2df;
  flex: 1;
  display: flex;
}

.custom-form-style .el-form-item .form-item-cont {
  padding: 10px 10px 10px 0;
}

.custom-form-style .el-form-item__label,
.custom-form-style .label-style {
  color: #333;
  font-weight: bold;
  border-right: none;
  background-color: #f3f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-form-style .el-form-item__content {
  flex: 1;
  padding-left: 15px;
  margin-left: 0 !important;
  border-left: 1px solid #ced2df;
  width: calc(100% - 150px);
}

.custom-form-style .el-input__inner {
  border: none;
}

.custom-form-style .el-textarea__inner {
  border-radius: 0;
  border: none;
}

.custom-form-style .el-cascader {
  width: 100% !important;
}

.custom-form-style .data-link-time {
  display: flex;
  gap: 10px;
  margin: 18px 0;
}

.custom-form-style .data-link-time .el-form-item {
  width: 200px !important;
}

.box-card.el-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.box-card.el-card .el-card__body {
  flex: 1;
  position: relative;
}

.updload-explain {
  display: block;
  padding-top: 5px;
  width: 100%;
  color: red;
  font-size: 14px;
}
